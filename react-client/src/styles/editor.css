/* TipTap Editor Styles */
.ProseMirror {
  outline: none;
  padding: 16px;
  min-height: 400px;
  font-family: system-ui, -apple-system, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
}

.ProseMirror p {
  margin: 0 0 16px 0;
}

.ProseMirror p:last-child {
  margin-bottom: 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.3;
}

.ProseMirror h1 {
  font-size: 2em;
}

.ProseMirror h2 {
  font-size: 1.5em;
}

.ProseMirror h3 {
  font-size: 1.25em;
}

.ProseMirror ul,
.ProseMirror ol {
  margin: 16px 0;
  padding-left: 24px;
}

.ProseMirror li {
  margin: 4px 0;
}

.ProseMirror blockquote {
  border-left: 4px solid #E5E7EB;
  margin: 16px 0;
  padding-left: 16px;
  color: #6B7280;
  font-style: italic;
}

.ProseMirror code {
  background-color: #F3F4F6;
  border-radius: 4px;
  padding: 2px 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.ProseMirror pre {
  background-color: #F3F4F6;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

.ProseMirror strong {
  font-weight: 600;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #E5E7EB;
  margin: 24px 0;
}

/* Collaboration cursor styles */
.collaboration-cursor__caret {
  border-left: 1px solid #0D0D0D;
  border-right: 1px solid #0D0D0D;
  margin-left: -1px;
  margin-right: -1px;
  pointer-events: none;
  position: relative;
  word-break: normal;
}

.collaboration-cursor__label {
  border-radius: 3px 3px 3px 0;
  color: #0D0D0D;
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  left: -1px;
  line-height: normal;
  padding: 0.1rem 0.3rem;
  position: absolute;
  top: -1.4em;
  user-select: none;
  white-space: nowrap;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  color: #9CA3AF;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Selection styles */
.ProseMirror ::selection {
  background-color: #DBEAFE;
}

.ProseMirror ::-moz-selection {
  background-color: #DBEAFE;
}
