// Простой тест для проверки работы collaboration.util.ts
const { tiptapExtensions, jsonToHtml, jsonToText, htmlToJson } = require('./dist/modules/editor/presentation/server/collaboration.util');

console.log('🧪 Тестирование collaboration.util.ts');

// Тестовый JSON контент
const testJson = {
    type: 'doc',
    content: [
        {
            type: 'heading',
            attrs: { level: 1 },
            content: [{ type: 'text', text: 'Заголовок тестового документа' }]
        },
        {
            type: 'paragraph',
            content: [
                { type: 'text', text: 'Это ' },
                { type: 'text', marks: [{ type: 'bold' }], text: 'жирный' },
                { type: 'text', text: ' текст с ' },
                { type: 'text', marks: [{ type: 'italic' }], text: 'курсивом' },
                { type: 'text', text: '.' }
            ]
        },
        {
            type: 'bulletList',
            content: [
                {
                    type: 'listItem',
                    content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Первый пункт списка' }] }]
                },
                {
                    type: 'listItem',
                    content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Второй пункт списка' }] }]
                }
            ]
        }
    ]
};

console.log('📝 Тестовый JSON:', JSON.stringify(testJson, null, 2));

try {
    // Тест 1: JSON to HTML
    console.log('\n🔄 Тест 1: JSON to HTML');
    const html = jsonToHtml(testJson);
    console.log('✅ HTML:', html);

    // Тест 2: JSON to Text
    console.log('\n🔄 Тест 2: JSON to Text');
    const text = jsonToText(testJson);
    console.log('✅ Text:', text);

    // Тест 3: HTML to JSON
    console.log('\n🔄 Тест 3: HTML to JSON');
    const backToJson = htmlToJson(html);
    console.log('✅ Back to JSON:', JSON.stringify(backToJson, null, 2));

    // Тест 4: Проверка расширений
    console.log('\n🔄 Тест 4: Проверка расширений');
    console.log('✅ Количество расширений:', tiptapExtensions.length);
    console.log('✅ Расширения загружены успешно');

    console.log('\n🎉 Все тесты прошли успешно!');

} catch (error) {
    console.error('❌ Ошибка в тестах:', error);
    process.exit(1);
}
