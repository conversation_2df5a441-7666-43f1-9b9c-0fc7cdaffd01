import { Extension, onLoadDocumentPayload, onStoreDocumentPayload } from '@hocuspocus/server';
import { TiptapTransformer } from '@hocuspocus/transformer';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { JSONContent } from '@tiptap/core';
import { isDeepStrictEqual } from 'util';
import * as Y from 'yjs';

import { DRIZZLE_DB } from '../../../../../drizzle/drizzle.module';
import { DrizzleDB } from '../../../../../drizzle/drizzle.types';
import { executeTx } from '../../../../../drizzle/drizzle.utils';
import { Page } from '../../../../../drizzle/types/entity.types';
import { IPageRepo } from '../../../application/repositories/page-repository.interface';
import { PageService } from '../../../application/services/page.service';
import { PAGE_REPO } from '../../../injects';
import { getPageId, jsonToText, tiptapExtensions } from '../collaboration.util';

@Injectable()
export class PersistenceExtension implements Extension {
    private readonly logger = new Logger(PersistenceExtension.name);

    constructor(
        private readonly pageService: PageService,
        @Inject(PAGE_REPO) private readonly pageRepo: IPageRepo,
        @Inject(DRIZZLE_DB) private readonly db: DrizzleDB,
    ) {}

    public async onLoadDocument(data: onLoadDocumentPayload) {
        const { documentName, document } = data;
        const pageId = getPageId(documentName);

        if (!document.isEmpty('default')) {
            return;
        }

        // if (!documentName) {
        //     return new Y.Doc();
        // }

        const page = await this.pageService.findByIdOrNull(pageId);
        if (!page) {
            this.logger.warn('page not found');
            return;
        }

        if (page.ydoc) {
            this.logger.debug(`ydoc loaded from db: ${pageId}`);
            const ydoc = new Y.Doc();
            Y.applyUpdate(ydoc, page.ydoc);
            return ydoc;
        }

        if (page.content) {
            this.logger.debug(`converting json to ydoc: ${pageId}`);
            const ydoc = TiptapTransformer.toYdoc(page.content, 'default', tiptapExtensions);
            Y.encodeStateAsUpdate(ydoc);
            return ydoc;
        }

        // TODO: в будущем не создаем документ через ws
        this.logger.debug(`Создание нового Y.Doc для: ${pageId}`);
        return new Y.Doc();
    }

    public async onStoreDocument(data: onStoreDocumentPayload) {
        const { documentName, document } = data;

        if (!documentName) {
            return;
        }

        const pageId = getPageId(documentName);

        const ydocState = Buffer.from(Y.encodeStateAsUpdate(document));

        // let page = await this.pageService.findByIdOrNull(pageId);
        // if (this.hasDataChanged(page, ydocState, pageId)) {
        //     await this.pageService.update(pageId, { });
        // }

        const tiptapJson: JSONContent = TiptapTransformer.fromYdoc(document, 'default') as JSONContent;
        let textContent: string = null;
        try {
            textContent = jsonToText(tiptapJson);
        } catch (err) {
            this.logger.warn('jsonToText ' + err);
        }

        try {
            await executeTx(this.db, async (trx) => {
                const page = await this.pageRepo.findById(pageId, trx);

                if (!page) {
                    this.logger.error(`Page with id ${pageId} not found`);
                    return;
                }

                if (isDeepStrictEqual(tiptapJson, page.content)) {
                    return;
                }

                await this.pageRepo.updatePage(
                    {
                        content: tiptapJson,
                        textContent,
                        ydoc: ydocState,
                    },
                    pageId,
                    trx,
                );

                this.logger.debug(`Page updated: ${pageId}`);
            });
        } catch (err) {
            this.logger.error(`Failed to update page ${pageId}`, err);
        }
    }

    // async onChange(data: onChangePayload) {
    //     void data;
    // }

    // async afterUnloadDocument(data: afterUnloadDocumentPayload): Promise<void> {
    //     void data;
    // }

    /**
     * Проверка изменений в бинарных данных
     */
    private hasDataChanged(existingPage: Page, newBinaryData: Buffer, pageId: string): boolean {
        if (existingPage.ydoc && Buffer.compare(existingPage.ydoc, newBinaryData) === 0) {
            this.logger.debug(`Изменения для документа ${pageId} не обнаружены`);
            return false;
        }
        return true;
    }
}
