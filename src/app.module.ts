import { Module } from '@nestjs/common';

import { CoreModule } from './modules/core/core.module';
import { EditorModule } from './modules/editor/editor.module';
import { QueueModule } from './modules/editor/infrastructure/queue/queue.module';
import { CollaborationModule } from './modules/editor/presentation/server/collaboration.module';

@Module({
    imports: [CoreModule, EditorModule, QueueModule, CollaborationModule],
})
export class AppModule {}
