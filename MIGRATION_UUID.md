# 🔄 Миграция UUID генерации из базы данных в приложение

## 📋 Обзор изменений

Перенесли генерацию UUID v7 из базы данных PostgreSQL в логику приложения с использованием библиотеки `uuid`.

## ✅ Преимущества

### 🚀 **Портабельность**
- ✅ Не требует создания функций в базе данных
- ✅ Не зависит от расширений PostgreSQL
- ✅ Легче миграция между разными СУБД

### 🔧 **Упрощение настройки**
- ✅ Убрали создание функции `gen_uuid_v7()` 
- ✅ Убрали зависимость от расширения `pgcrypto`
- ✅ Упростили настройку тестовой среды

### 📊 **Контроль**
- ✅ UUID генерируется в логике приложения
- ✅ Лучшая отладка и логирование
- ✅ Единообразная генерация во всех средах

## 🔧 Технические изменения

### 1. Установка зависимостей
```bash
pnpm add uuid
pnpm add -D @types/uuid
```

### 2. Обновление схемы базы данных
**Было:**
```typescript
id: uuid('id')
    .primaryKey()
    .default(sql`gen_uuid_v7()`),
```

**Стало:**
```typescript
id: uuid('id').primaryKey(),
```

### 3. Обновление PageService
**Добавлено:**
```typescript
import { v7 as uuidv7 } from 'uuid';

async create(createPageDto: CreatePageDto): Promise<Page> {
    const pageId = uuidv7();
    const insertableData: InsertablePage = {
        id: pageId,
        title: createPageDto.title,
        content: createPageDto.content,
        textContent: createPageDto.textContent,
        isLocked: createPageDto.isLocked || false,
    };
    // ...
}
```

### 4. Упрощение тестовой настройки
**Убрано из jest.setup.cjs:**
```javascript
// Создание функции gen_uuid_v7() больше не нужно
await client.query('CREATE EXTENSION IF NOT EXISTS pgcrypto;');
await client.query(`CREATE OR REPLACE FUNCTION gen_uuid_v7() ...`);
```

**Заменено на:**
```javascript
// UUID generation is now handled in application logic using uuid library
```

## 🧪 Тестирование

### Интеграционные тесты
```bash
pnpm test:integration
```
**Результат:** ✅ Все 37 тестов прошли успешно

### Ручное тестирование
1. **Запуск приложения:**
   ```bash
   APP_PORT=3033 npx ts-node src/main.ts
   ```

2. **Запуск debug клиента:**
   ```bash
   pnpm client:debug
   ```

3. **Тестирование создания страниц:**
   - Откройте http://localhost:8081/debug-client.html
   - Измените Server URL на `http://localhost:3001`
   - Создайте новую страницу
   - Проверьте сгенерированный UUID v7

## 📁 Измененные файлы

### Основные файлы
- ✅ `src/database/schema/pages.ts` - убрали default значение
- ✅ `src/modules/editor/application/services/page.service.ts` - добавили генерацию UUID
- ✅ `tests/config/jest.setup.cjs` - убрали создание функции
- ✅ `package.json` - добавили зависимости

### Миграции
- ✅ `src/database/migrations/0000_wonderful_jetstream.sql` - новая схема без default

## 🔍 UUID v7 формат

UUID v7 содержит:
- **48 бит**: Unix timestamp в миллисекундах
- **12 бит**: случайные данные
- **2 бита**: версия (0111)
- **62 бита**: случайные данные

**Пример:** `01936b2e-1234-7abc-8def-123456789abc`

## 🚀 Готово к использованию

**Система полностью переведена на генерацию UUID в приложении!**

### Команды для тестирования:
```bash
# Запуск приложения
APP_PORT=3001 npx ts-node src/main.ts

# Запуск debug клиента
pnpm client:debug

# Запуск тестов
pnpm test:integration
```

### Проверка работы:
1. ✅ Приложение запускается без ошибок
2. ✅ Создание страниц работает корректно
3. ✅ UUID генерируются в формате v7
4. ✅ Все тесты проходят успешно
5. ✅ База данных не требует дополнительных функций

**🎯 Миграция завершена успешно!**
